import os
import json
import requests
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTextEdit, 
                           QLabel, QScrollArea, QFrame, QWidget, QLineEdit)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QPixmap

class ChatMessageThread(QThread):
    """处理聊天消息的线程"""
    message_received = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, url, api_key, user_id, message):
        super().__init__()
        self.url = url
        self.api_key = api_key
        self.user_id = user_id
        self.message = message
        
    def run(self):
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "query": self.message,
                "user": self.user_id,
                "response_mode": "blocking",  # 使用阻塞模式简化处理
                "inputs": {}
            }
            
            response = requests.post(
                f"{self.url}/chat-messages", 
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                self.message_received.emit(result.get('answer', '抱歉，无法获取回复'))
            else:
                self.error_occurred.emit(f"请求失败: {response.status_code}")
        except Exception as e:
            self.error_occurred.emit(f"发生错误: {str(e)}")

class ChatDialog(QDialog):
    """简单的聊天对话框，显示在右下角"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("工具箱助手")
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(380, 500)
        
        # 获取当前分类
        self.parent_window = parent
        self.current_category = ""
        if hasattr(parent, 'category_list') and parent.category_list.currentItem():
            self.current_category = parent.category_list.currentItem().text()
        
        # API配置
        self.base_url = "http://127.0.0.1/v1"
        self.api_key = "app-Ap5WzR0fPADjUgK9BA3zo0kp"  # 这里应该使用实际的API密钥
        self.user_id = "user_" + os.path.basename(os.getcwd())  # 简单生成一个用户ID
        
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题栏
        title_bar = QFrame()
        title_bar.setStyleSheet("background-color: #1C64F2; border-radius: 5px;")
        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(10, 5, 10, 5)
        
        # 根据当前分类设置标题
        title_text = f"{self.current_category}助手" if self.current_category else "工具箱助手"
        title_label = QLabel(title_text)
        title_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        title_bar_layout.addWidget(title_label)
        
        close_btn = QPushButton()
        close_btn.setIcon(QIcon("../image/close.png"))  # 需要一个关闭图标
        close_btn.setFixedSize(24, 24)
        close_btn.setStyleSheet("QPushButton { border: none; background-color: transparent; }")
        close_btn.clicked.connect(self.close)
        title_bar_layout.addWidget(close_btn, alignment=Qt.AlignRight)
        
        layout.addWidget(title_bar)
        
        # 聊天历史
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                padding: 10px;
                font-size: 13px;
            }
        """)
        layout.addWidget(self.chat_history)
        
        # 输入区域
        input_container = QFrame()
        input_container.setStyleSheet("background-color: white; border: 1px solid #e0e0e0; border-radius: 5px;")
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(5, 5, 5, 5)
        input_layout.setSpacing(5)
        
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("输入您的问题...")
        self.input_field.setStyleSheet("""
            QLineEdit {
                border: none;
                padding: 5px;
                font-size: 13px;
            }
        """)
        self.input_field.returnPressed.connect(self.send_message)
        input_layout.addWidget(self.input_field)
        
        send_btn = QPushButton("发送")
        send_btn.setStyleSheet("""
            QPushButton {
                background-color: #1C64F2;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #165CE3;
            }
        """)
        send_btn.clicked.connect(self.send_message)
        input_layout.addWidget(send_btn)
        
        layout.addWidget(input_container)
        
        # 添加欢迎消息
        welcome_message = self.get_welcome_message()
        self.append_message("助手", welcome_message)
        
    def get_welcome_message(self):
        """根据当前分类生成欢迎消息"""
        if self.current_category == "安装工具":
            return "您好！我是安装工具助手。我可以帮您解答关于设备安装、配置和初始化的问题。请问有什么需要帮助的？"
        elif self.current_category == "调试工具":
            return "您好！我是调试工具助手。我可以帮您解决调试过程中遇到的问题，包括参数配置、信号检测等。请问有什么需要帮助的？"
        elif self.current_category == "测试工具":
            return "您好！我是测试工具助手。我可以帮您解答关于测试流程、测试方法和结果分析的问题。请问有什么需要帮助的？"
        elif self.current_category == "标定工具":
            return "您好！我是标定工具助手。我可以帮您解答关于传感器标定、系统校准等问题。请问有什么需要帮助的？"
        elif self.current_category == "便捷工具":
            return "您好！我是便捷工具助手。我可以帮您解答关于各种辅助工具使用方法的问题。请问有什么需要帮助的？"
        elif self.current_category == "信息查询":
            return "您好！我是信息查询助手。我可以帮您查找和解读各类技术资料和参数信息。请问需要查询什么信息？"
        else:
            return "您好！我是工具箱助手，有什么可以帮助您的？"
        
    def append_message(self, sender, text):
        """添加消息到聊天历史"""
        if sender == "用户":
            style = "background-color: #e3f2fd; color: #333; border-radius: 5px; padding: 5px; margin: 2px 20px 2px 5px;"
            align = "right"
        else:
            style = "background-color: #f5f5f5; color: #333; border-radius: 5px; padding: 5px; margin: 2px 5px 2px 20px;"
            align = "left"
            
        self.chat_history.append(f'<div style="{style}" align="{align}"><b>{sender}:</b><br>{text}</div>')
        
        # 滚动到底部
        scrollbar = self.chat_history.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def send_message(self):
        """发送用户消息并获取回复"""
        message = self.input_field.text().strip()
        if not message:
            return
            
        # 显示用户消息
        self.append_message("用户", message)
        self.input_field.clear()
        
        # 显示正在输入提示
        self.append_message("助手", "正在思考...")
        
        # 创建线程发送API请求
        self.thread = ChatMessageThread(self.base_url, self.api_key, self.user_id, message)
        self.thread.message_received.connect(self.handle_response)
        self.thread.error_occurred.connect(self.handle_error)
        self.thread.start()
        
    def handle_response(self, response):
        """处理API响应"""
        # 移除"正在思考..."消息
        cursor = self.chat_history.textCursor()
        cursor.movePosition(cursor.End)
        cursor.select(cursor.BlockUnderCursor)
        cursor.removeSelectedText()
        cursor.deletePreviousChar()  # 删除换行符
        
        # 添加回复
        self.append_message("助手", response)
        
    def handle_error(self, error_message):
        """处理错误"""
        # 移除"正在思考..."消息
        cursor = self.chat_history.textCursor()
        cursor.movePosition(cursor.End)
        cursor.select(cursor.BlockUnderCursor)
        cursor.removeSelectedText()
        cursor.deletePreviousChar()  # 删除换行符
        
        # 添加错误消息
        self.append_message("助手", f"抱歉，发生了错误: {error_message}")
        
    def mousePressEvent(self, event):
        """支持窗口拖动"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event):
        """处理窗口拖动"""
        if event.buttons() & Qt.LeftButton:
            self.move(event.globalPos() - self.drag_position)
            event.accept() 