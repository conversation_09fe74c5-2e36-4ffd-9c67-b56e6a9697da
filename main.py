import sys
import os
import ctypes
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QIcon
from main_window import MainWindow

def main():
    # 确保当前工作目录是程序所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)

    # 设置应用程序用户模型ID，让Windows识别这是一个独立的应用程序
    # 这样可以避免任务栏显示Python图标
    try:
        # 设置应用程序ID，这会让Windows将此应用视为独立应用而不是Python脚本
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("SensorToolbox.Main.1.0")
    except:
        pass  # 如果设置失败，继续运行

    app = QApplication(sys.argv)

    # 设置应用程序图标（这会影响任务栏图标）
    icon_path = "../image/start.ico"
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    else:
        # 如果ico文件不存在，尝试使用png文件
        png_icon_path = "../image/start.png"
        if os.path.exists(png_icon_path):
            app.setWindowIcon(QIcon(png_icon_path))

    window = MainWindow()
    window.show()

    # 使用QTimer延迟调用，确保主窗口完全加载后再显示首页
    QTimer.singleShot(100, window.show_home_page)

    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 