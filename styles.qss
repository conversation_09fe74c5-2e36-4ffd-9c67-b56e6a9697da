/* 主窗口样式 */
QMainWindow {
    background-color: #f5f6fa;
}

/* 左侧分类列表样式 */
QListWidget {
    background-color: #2f3640;
    border: none;
    border-radius: 6px;
    padding: 8px;
    color: #dcdde1;
}

QListWidget::item {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}

QListWidget::item:selected {
    background-color: #487eb0;
    color: white;
}

QListWidget::item:hover {
    background-color: #40739e;
}

/* 按钮样式 */
QPushButton {
    background-color: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
}

QPushButton:hover {
    background-color: #f8f9fa;
    border: 1px solid #487eb0;
}

QPushButton:pressed {
    background-color: #e9ecef;
}

/* 添加应用按钮特殊样式 */
QPushButton#addButton {
    font-size: 28px;
    color: #487eb0;
    border: none;
    background-color: transparent;
}

QPushButton#addButton:hover {
    color: #40739e;
}

/* 通用标签样式 - 用于分类标题等 */
QLabel {
    color: #333333;
    font-size: 16px;
    padding: 6px;
    background-color: transparent;
}

/* 对话框样式 */
QDialog {
    background-color: #f5f6fa;
}

QLineEdit, QTextEdit {
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    padding: 15px;
    background-color: white;
    font-size: 16px;
}

QLineEdit:focus, QTextEdit:focus {
    border: 1px solid #487eb0;
}

/* 下拉框样式 */
QComboBox {
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    padding: 12px;
    background-color: white;
    font-size: 16px;
}

QComboBox:hover {
    border: 1px solid #487eb0;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

/* 菜单样式 */
QMenu {
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 4px;
}

QMenu::item {
    padding: 6px 16px;
    border-radius: 2px;
}

QMenu::item:selected {
    background-color: #487eb0;
    color: white;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background-color: #f5f6fa;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c8d6e5;
    border-radius: 6px;
    min-height: 40px;
}

QScrollBar::handle:vertical:hover {
    background-color: #487eb0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* 工具提示样式 */
QToolTip {
    background-color: #2f3640;
    color: white;
    border: none;
    padding: 10px 14px;
    border-radius: 6px;
    font-size: 14px;
}