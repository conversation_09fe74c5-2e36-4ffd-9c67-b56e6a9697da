# 流程生成助手使用说明

## 功能概述

流程生成助手是一个智能化的操作流程指导工具，集成在主工具箱中。它可以根据用户输入的关键词自动生成相应的操作流程，并提供可视化的流程图、详细的操作说明、所需工具列表和注意事项。

## 主要功能

### 1. 智能流程生成
- 支持关键词匹配，自动生成相应的操作流程
- 支持的流程类型：
  - 毫米波雷达调试与测试
  - 激光雷达标定
  - 摄像头标定
  - 设备维护

### 2. 可视化流程图
- 横向流程图显示，直观展示操作步骤
- 当前步骤高亮显示
- 支持语音播放功能（点击喇叭图标）

### 3. 详细操作指导
- **技能要点**：显示当前步骤的详细操作说明
- **所需工具**：列出执行当前步骤需要的工具和设备
- **注意事项**：提醒操作中需要注意的安全事项和技术要点

### 4. 交互式操作
- 上一步/下一步按钮控制流程进度
- 查看文档按钮（如果有相关文档）
- 语音播放功能

## 使用方法

### 启动流程生成助手
1. 在主工具箱中点击"流程生成助手"
2. 系统将显示欢迎界面和使用提示

### 生成操作流程
1. 在底部输入框中输入流程关键词，例如：
   - "毫米波雷达调试"
   - "激光雷达标定"
   - "摄像头标定"
   - "设备维护"

2. 点击"点击生成"按钮或按回车键

3. 系统将自动匹配并生成相应的操作流程

### 执行流程步骤
1. 查看流程图了解整体流程
2. 阅读"技能要点"中的详细操作说明
3. 准备"所需工具"中列出的工具和设备
4. 注意"注意事项"中的安全提醒
5. 完成当前步骤后，点击"下一步"按钮
6. 重复以上步骤直到流程完成

### 语音功能
- 点击流程图中每个步骤右上角的喇叭图标
- 系统将播放该步骤的语音说明

## 支持的关键词

### 毫米波雷达相关
- 毫米波、雷达、调试、测试、radar

### 激光雷达相关
- 激光雷达、激光、lidar、标定

### 摄像头相关
- 摄像头、相机、camera、标定

### 设备维护相关
- 设备、维护、保养、维修、检查、清洁

## 技术特性

- **兼容性**：完全兼容现有的主工具箱系统
- **响应速度**：快速关键词匹配和流程生成
- **用户体验**：直观的可视化界面和交互设计
- **扩展性**：支持添加新的流程模板

## 注意事项

1. 确保在执行任何操作前仔细阅读注意事项
2. 按照流程顺序执行，不要跳过重要步骤
3. 如有疑问，可查看相关文档（如果提供）
4. 语音功能需要系统支持TTS（文本转语音）

## 故障排除

如果遇到问题：
1. 检查输入的关键词是否正确
2. 确认 process_flow.json 文件存在且格式正确
3. 检查语音功能是否需要安装额外的语音引擎
4. 重启应用程序

---

*流程生成助手已成功集成到主工具箱中，可以通过导航栏直接访问。*
